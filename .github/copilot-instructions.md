---
applyTo: '**'
---

# Project Coding Guidelines

This document provides comprehensive coding standards for AI assistants working on this project.

## Project Overview

This project (`@kdt-farm/solana-grpc-client`) is a TypeScript library that implements clients for multiple Solana gRPC services:

- **YellowStone Geyser gRPC** - Real-time Solana blockchain data streaming
- **Corvus ARPC** - Advanced RPC client for Solana
- **Shred Forwarder** - Transaction forwarding and processing service
- **Jito Shred Stream** - Jito MEV shred streaming and heartbeat service

The library is designed to provide a type-safe and easy-to-use interface for interacting with these Solana gRPC services. Each client follows a consistent architecture pattern with proper streaming support, error handling, and TypeScript integration.

## Tech Stack

- **Node.js LTS** - Runtime environment (>=22.15.0)
- **TypeScript** - Programming language with static typing
- **PNPM** - Package manager
- **@kdt310722/tsconfig** - TypeScript configuration
- **@kdt310722/eslint-config** - ESLint configuration and style enforcement
- **gRPC** - Remote procedure call framework
- **Protobuf** - Protocol Buffers for serialization
- **ts-proto** - TypeScript code generation from proto files
- **tsup** - TypeScript bundler for building ESM/CJS outputs

## Proto Files and gRPC Clients

### Working with Proto Files

- Proto files are managed as git submodules in the `protos/` directory
- Never modify the proto files directly as they are maintained by their respective projects
- To generate TypeScript code from proto files, use the provided scripts:
    - `pnpm proto:generate` - Generate all clients
    - `pnpm proto:generate:yellowstone` - Generate only YellowStone client
    - `pnpm proto:generate:corvus` - Generate only Corvus client
    - `pnpm proto:generate:shred-forwarder` - Generate only Shred Forwarder client
    - `pnpm proto:generate:jito-shred-stream` - Generate only Jito Shred Stream client

### Client Implementation Guidelines

- Keep client implementations in their respective directories under `src/clients/`
- Do not modify generated code in the `generated/` directories
- Create wrapper classes that provide a more user-friendly API around the generated clients
- Export client implementations from the respective `index.ts` files
- Use stream wrappers for handling gRPC streaming connections
- Implement proper error handling and connection management for gRPC clients

## TypeScript Configuration

### Project-Specific Settings

- Target ES modules (`"type": "module"` in package.json)
- Use strict TypeScript configuration from `@kdt310722/tsconfig`

### Type Safety Rules

- Use TypeScript's strict type checking
- Prefer `unknown` over `any` type
- Use type-only imports: `import type { Type }` for types only
- Prefer inline type imports: `import { type Type, value }` when mixing
- Define clear types for functions and variables
- Only specify return types for complex types or when not obvious from code
- Use utility types (`Pick`, `Omit`, `Partial`) for type manipulation
- Extract nested interfaces into separate interfaces for reusability

## Code Formatting

### Indentation & Spacing

- Use 4 spaces for indentation, never tabs
- No enforced maximum line length
- Remove trailing whitespace
- Add blank line at end of files
- Use LF (`\n`) line endings
- Place spaces inside object braces: `{ like: this }`
- Add space before opening braces: `function name() {`

### Punctuation & Symbols

- No semicolons at end of statements
- Use single quotes (`'`) for strings in JS/TS
- Use double quotes (`"`) for JSX attributes
- Use trailing commas in ES5 style:
    - Always for multiline arrays and objects
    - Never for imports/exports
    - Never for function parameters
- Always use parentheses with arrow functions: `(param) => {}`
- Use "one true brace style": opening brace on same line
- Closing bracket on new line
- Empty arrow function bodies on single line: `() => {}`

### Line Breaking & Padding

- Add blank lines before and after major code blocks
- Add blank line before `return` statements
- Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
- No blank lines between `case` statements in `switch`
- Add blank line between variable assignment and subsequent method calls

## Import Organization

### Import Order (Strict)

1. Node.js built-in modules (with `node:` prefix)
2. External libraries (alphabetical)
3. Side-effect imports (`import 'module'`)
4. Internal modules (by proximity: `../`, `./`)

### Import Rules

- Remove unused imports automatically
- Keep import statements at top of file
- Keep each import on one line (no line breaks in import statements)
- Keep export statements on one line without line breaks
- No import type side effects

## Function & Variable Rules

### Function Guidelines

- Keep return statements clear and explicit
- Maximum 30 lines per function (recommended)
- Maximum 3 levels of nesting depth
- Prefix unused parameters with underscore: `_error`, `_unused`
- Use descriptive names indicating purpose
- Prefer arrow functions with direct return for simple transformations
- Keep entire return expressions on one line when possible
- Extract complex inline objects to variables for readability
- For async functions returning single awaited expression, return directly
- Omit `await` in direct returns if not needed for error handling
- **Function signatures should be on single lines when possible** for better readability
- Use concise forms for simple conditional logic (e.g., logical OR operator for simple conditions)
- Prefer single-line implementations for simple timeout/conditional logic
- **Prefer single-line returns for simple method implementations** - When a method simply calls another function or returns a direct expression, use single-line returns without intermediate variables
- **Use consistent parameter destructuring patterns** - When functions accept options objects, use destructuring with default values in the parameter signature (e.g., `{ option1 = default1, option2 = default2 } = {}`) for consistency across the codebase

### Variable Rules

- Use camelCase for variables and functions
- Use PascalCase for classes and components
- Use UPPERCASE_SNAKE_CASE for global constants
- Keep function parameters on same line when reasonable
- Group variable declarations by type (`let` vs `const`) with blank lines between groups
- Apply grouping only to single-line declarations (multi-line declarations remain separate)

## Naming Conventions

### File & Directory Naming

- Files: kebab-case for regular files, PascalCase for component files
- Directories: kebab-case grouped by functionality
- TypeScript files: `.ts` extension
- Generated files: maintain original naming from proto generation

### Code Naming

- Variables and functions: camelCase
- Classes and interfaces: PascalCase
- Constants: UPPERCASE_SNAKE_CASE
- Private class fields: prefer `#privateField` syntax

## Code Organization

### File Structure

1. Imports (following grouping rules)
2. Type definitions and interfaces
3. Constants and configuration
4. Implementation (functions, classes)
5. Exports (prefer named exports, alphabetically organized)

### Module Organization

- Each client has its own directory under `src/clients/`
- Each module exports through `index.ts` file
- Use `export * from './file'` pattern for re-exports
- Use `export type * from './types'` for type-only exports
- Group related functionality in the same module

### Export Patterns

- Use named exports over default exports
- Export types separately: `export type * from './types'`
- Main index exports modules as namespaces when appropriate
- Each module index re-exports all functionality from its files

### Class Organization

Structure class members in this order:

1. Public properties
2. Protected properties
3. Private properties (prefer `#privateField` syntax)
4. Constructor
5. Static methods
6. Instance methods (public → protected → private)

#### Class Formatting Rules

- Add blank lines between access modifier groups
- Group properties by access modifier with spacing
- Prefer inline access modifiers for simple constructors: `public constructor(private readonly config: Config) {}`
- Keep constructor declarations on single line when reasonable

## Code Quality Guidelines

### Performance Considerations

- Use appropriate data structures for the use case
- Avoid unnecessary async/await in direct returns
- Prefer native array methods over manual loops when appropriate
- Optimize gRPC stream handling for better performance

### Error Handling

- Use proper error types and messages
- Handle edge cases gracefully
- Validate input parameters when necessary
- Implement proper error handling for gRPC connections and streams
- Handle stream disconnections and reconnections gracefully

### gRPC and Stream Specific Guidelines

- Use stream wrappers for consistent error handling and event management
- Implement proper cleanup for gRPC streams and connections
- Handle backpressure and flow control in streaming scenarios
- Use appropriate timeouts for gRPC calls
- Implement heartbeat mechanisms for long-lived connections

## Adding New gRPC Clients

When adding a new gRPC client to this project, follow this comprehensive checklist to ensure consistency with the existing architecture:

### 0. **MANDATORY: Study Existing Implementations First**

**Before implementing any new client, you MUST thoroughly analyze existing client implementations:**

- **Study `YellowstoneGeyserClient`** (`src/clients/yellowstone/client.ts`):
  - Examine constructor pattern and options handling
  - Understand how unary methods are implemented using `call` utility
  - Study the `createStream()` method implementation
  - Analyze error handling patterns

- **Study `CorvusArpcClient`** (`src/clients/corvus/client.ts`):
  - Compare constructor patterns with Yellowstone
  - Understand differences in client configuration
  - Examine stream wrapper integration

- **Study `ShredForwarderClient`** (`src/clients/shred-forwarder/client.ts`):
  - Understand the simplest client implementation pattern
  - Study how ping methods are implemented
  - Examine stream wrapper usage

- **Study Stream Wrappers**:
  - `YellowstoneGeyserStreamWrapper` - Complex duplex streaming
  - `CorvusArpcStreamWrapper` - How it extends Yellowstone patterns
  - `ShredForwarderStreamWrapper` - Simple streaming implementation

- **Study Utils Patterns**:
  - Options resolvers in each client's `utils/options.ts`
  - Export patterns in `utils/index.ts`
  - Stream wrapper implementations

**Only after understanding these patterns should you proceed with implementation. Your new client MUST follow the same architectural patterns, naming conventions, and code organization.**

### 1. Proto Files Setup

- **Add proto files**: Place proto files in `protos/{client-name}/` directory
- **Git submodules**: If proto files are from external repository, add as git submodule
- **Proto structure**: Ensure proto files define both unary and streaming RPC methods when applicable

### 2. Build Configuration

- **Package.json scripts**: Add generation script following the pattern:
  ```json
  "proto:generate:{client-name}": "rm -rf ./src/clients/{client-name}/generated && mkdir -p ./src/clients/{client-name}/generated && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputServices=grpc-js,forceLong=bigint,env=node,esModuleInterop=true,exportCommonSymbols=false,useAbortSignal=true,useAsyncIterable=true --experimental_allow_proto3_optional --ts_proto_out=./src/clients/{client-name}/generated --proto_path=./protos/{client-name} ./protos/{client-name}/*.proto"
  ```
- **Update main script**: Add new client to `proto:generate` script
- **Tsup configuration**: Add entry point to `tsup.config.ts`:
  ```typescript
  entry: [..., 'src/clients/{client-name}/index.ts']
  ```

### 3. Directory Structure

Create the following directory structure under `src/clients/{client-name}/`:

```
src/clients/{client-name}/
├── client.ts                 # Main client class
├── index.ts                  # Export file
├── generated/                # Generated proto code (auto-generated)
└── utils/                    # Client-specific utilities
    ├── index.ts              # Utils exports
    └── stream-wrapper.ts     # Stream wrapper implementation
```

### 4. Client Implementation

- **Main client class** (`client.ts`):
  - Follow naming pattern: `{ClientName}Client` (e.g., `ShredForwarderClient`)
  - Extend from generated gRPC client using `createGrpcClient` utility
  - Implement unary methods using `call` utility function
  - Implement `createStream()` method for streaming operations
  - Use consistent constructor pattern with options parameter

- **Stream wrapper** (`utils/stream-wrapper.ts`):
  - Extend from appropriate base class (`StreamWrapper` or `DuplexStreamWrapper`)
  - Implement ping detection methods if applicable
  - Handle client-specific streaming patterns
  - Follow naming pattern: `{ClientName}StreamWrapper`

### 5. Type Safety and Exports

**MANDATORY: Export ALL Files Rule**

**You MUST export ALL files from the client directory, including ALL generated files:**

- **Index exports** (`index.ts`) - Export EVERYTHING:
  ```typescript
  export * from './client'
  export * from './generated/{proto-file-name}'
  export * from './generated/google/protobuf/timestamp'
  export * from './utils'

  // Export ALL other generated files if they exist
  // Example: if there are additional generated files, export them too:
  // export * from './generated/other-proto-file'
  // export * from './generated/common-types'
  ```

- **Utils exports** (`utils/index.ts`) - Export ALL utils:
  ```typescript
  export * from './stream-wrapper'
  // Export ALL other utils files if they exist:
  // export * from './options'
  // export * from './helpers'
  // export * from './types'
  ```

- **Main clients index**: Update `src/clients/index.ts`:
  ```typescript
  export * as {clientName} from './{client-name}'
  ```

**Important**: The export rule ensures that users have access to ALL types, interfaces, and utilities from the client, including all generated protobuf types and helper functions. Never selectively export - always use `export *` to export everything from each file/directory.

### 6. Package Distribution

- **Package.json exports**: Add export paths following the pattern:
  ```json
  "./{client-name}": {
    "types": "./dist/types/clients/{client-name}/index.d.ts",
    "require": "./dist/clients/{client-name}/index.cjs",
    "default": "./dist/clients/{client-name}/index.js"
  },
  "./clients/{client-name}": {
    "types": "./dist/types/clients/{client-name}/index.d.ts",
    "require": "./dist/clients/{client-name}/index.cjs",
    "default": "./dist/clients/{client-name}/index.js"
  }
  ```

### 7. Implementation Checklist

Before considering a new client complete, verify:

- [ ] **MANDATORY**: Studied all existing client implementations thoroughly
- [ ] **MANDATORY**: All files are exported using `export *` pattern (no selective exports)
- [ ] Proto generation script works correctly
- [ ] Generated TypeScript code compiles without errors
- [ ] Main client class follows existing patterns exactly
- [ ] Stream wrapper implements appropriate base class
- [ ] All exports are properly configured (ALL files exported)
- [ ] Utils index exports ALL utility files
- [ ] Main index exports ALL generated files
- [ ] Build process includes new client
- [ ] Package.json exports are updated
- [ ] TypeScript compilation passes
- [ ] Client can be imported from main package
- [ ] Client can be imported via direct paths
- [ ] No unused imports or variables
- [ ] Follows all coding standards in this document
- [ ] Architecture matches existing clients exactly

## Implementation Guidelines

### Scope Limitation Rule

- **Only implement exactly what is requested in the task**
- Do not create additional code, features, or functionality beyond the explicit requirements
- If you think there might be related improvements or additions, ask the user first before implementing
- Focus on the specific requirements and avoid scope creep
- Stick to the minimal viable implementation that satisfies the request

### Default Exclusions Rule

By default, **skip the following unless explicitly requested**:

- **Input validation and parameter checking** - Only add when specifically asked
- **Documentation** - Comments, JSDoc, README updates, or inline documentation
- **Example implementations** - Only create when the user asks for examples

**Important**: Only create these items when the user specifically asks for them in their request. This keeps implementations focused and prevents unnecessary work.

### Code Documentation Rule

- **Never generate comments in source code by default** - Code should be self-documenting through clear naming and structure
- Only add comments when explicitly requested or when code behavior is genuinely non-obvious
- Prefer refactoring unclear code over adding explanatory comments
- Use meaningful variable and function names that eliminate the need for comments

### Code Quality Checks Rule

- **Never run automated checks unless explicitly requested** - Do not automatically run ESLint, TypeScript checks, or other validation tools
- Only execute `pnpm lint`, `pnpm typecheck`, or similar commands when the user specifically asks for them
- Focus on implementing the requested changes without automatic validation unless verification is requested
- Let the user decide when to run quality checks and validation

## Code Reuse Guidelines

### Reuse Principles

- Follow Open/Closed principle: extend without modifying existing code
- Prefer composition over inheritance
- Extract reusable logic into utility functions
- Maintain backward compatibility when extending functionality

### Avoiding Duplication

- Follow DRY (Don't Repeat Yourself) principle
- Apply Rule of Three: if code is copy-pasted 3 times, extract it into reusable function
- Search existing utilities before implementing new functionality

### Maintainability

- Keep functions focused on single responsibility
- Use meaningful variable and function names
- Maintain consistent code organization across modules
- Use TypeScript's type system to prevent runtime errors

## Client Development Best Practices

### Architecture Consistency

- **MANDATORY: Study existing implementations first**: Before writing any code, thoroughly analyze all existing client implementations to understand patterns
- **Follow existing patterns exactly**: Study `YellowstoneGeyserClient`, `CorvusArpcClient`, and `ShredForwarderClient` implementations
- **Consistent naming**: Use `{ServiceName}Client` pattern for main client classes
- **Stream wrappers**: Always provide stream wrapper classes for streaming operations
- **Error handling**: Implement consistent error handling across all clients
- **Export everything**: Always use `export *` to export ALL files, including ALL generated files

### Code Quality for Clients

- **Type safety**: Leverage generated TypeScript types from proto files
- **Resource management**: Properly handle gRPC connection lifecycle
- **Stream management**: Implement proper cleanup and error recovery for streams
- **Options pattern**: Use consistent options objects with sensible defaults

### Integration Guidelines

- **Namespace exports**: Export clients under descriptive namespaces in main index
- **Multiple import paths**: Support both namespace and direct import patterns
- **Backward compatibility**: Ensure new clients don't break existing functionality
- **Documentation**: Keep this guidelines document updated when adding new clients

### Performance Considerations

- **Lazy loading**: Generate clients only when needed
- **Connection pooling**: Reuse gRPC connections where appropriate
- **Stream optimization**: Implement efficient streaming patterns
- **Memory management**: Avoid memory leaks in long-running streams
